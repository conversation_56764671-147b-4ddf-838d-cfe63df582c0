import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

import '../../generated/l10n.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../widgets/ios_style_card.dart';
import '../../widgets/ios_style_search_bar.dart';
import '../../widgets/ios_style_transitions.dart';
import '../../ytmusic/ytmusic.dart';
import 'section_item.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final YTMusic ytMusic = GetIt.I<YTMusic>();
  late ScrollController _scrollController;
  late List chips = [];
  late List sections = [];
  int page = 0;
  String? continuation;
  bool initialLoading = true;
  bool nextLoading = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    fetchHome();
  }

  _scrollListener() async {
    if (initialLoading || nextLoading || continuation == null) {
      return;
    }

    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      await fetchNext();
    }
  }

  fetchHome() async {
    setState(() {
      initialLoading = true;
      nextLoading = false;
    });
    Map<String, dynamic> home = await ytMusic.browse();
    if (mounted) {
      setState(() {
        initialLoading = false;
        nextLoading = false;
        chips = home['chips'];
        sections = home['sections'];
        continuation = home['continuation'];
      });
    }
  }

  refresh() async {
    if (initialLoading) return;
    Map<String, dynamic> home = await ytMusic.browse();
    if (mounted) {
      setState(() {
        initialLoading = false;
        nextLoading = false;
        chips = home['chips'];
        sections = home['sections'];
        continuation = home['continuation'];
      });
    }
  }

  fetchNext() async {
    if (continuation == null) return;
    setState(() {
      nextLoading = true;
    });
    Map<String, dynamic> home =
        await ytMusic.browseContinuation(additionalParams: continuation!);
    List<Map<String, dynamic>> secs =
        home['sections'].cast<Map<String, dynamic>>();
    if (mounted) {
      setState(() {
        sections.addAll(secs);
        continuation = home['continuation'];
        nextLoading = false;
      });
    }
  }

  Widget _horizontalChipsRow(List data) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: data.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          final element = data[index];
          return IOSStyleChip(
            text: element['title'] ?? '',
            onTap: () => context.go('/chip', extra: element),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveScaffold(
      appBar: PreferredSize(
        preferredSize: const AdaptiveAppBar().preferredSize,
        child: AdaptiveAppBar(
          automaticallyImplyLeading: false,
          title: Material(
            color: Colors.transparent,
            child: LayoutBuilder(builder: (context, constraints) {
              return Row(
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth > 400
                            ? (400)
                            : constraints.maxWidth),
                    child: IOSStyleSearchBar(
                      hintText: S.of(context).Search_Gyawun,
                      readOnly: true,
                      onTap: () => context.go('/search'),
                    ),
                  ),
                ],
              );
            }),
          ),
          centerTitle: false,
        ),
      ),
      body: initialLoading
          ? const Center(child: AdaptiveProgressRing())
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).scaffoldBackgroundColor,
                    Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.95),
                  ],
                ),
              ),
              child: RefreshIndicator(
                onRefresh: () => refresh(),
                color: Theme.of(context).colorScheme.primary,
                backgroundColor: Theme.of(context).colorScheme.surface,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.only(top: 8, bottom: 24),
                  controller: _scrollController,
                  physics: const BouncingScrollPhysics(),
                  child: SafeArea(
                    child: Column(
                      children: [
                        _horizontalChipsRow(chips),
                        const SizedBox(height: 8),
                        ...sections.asMap().entries.map((entry) {
                          final index = entry.key;
                          final section = entry.value;
                          return IOSStyleAnimatedContainer(
                            duration: Duration(milliseconds: 300 + (index * 100)),
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: SectionItem(section: section),
                            ),
                          );
                        }),
                        if (!nextLoading && continuation != null)
                          const SizedBox(height: 50),
                        if (nextLoading)
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface.withOpacity(0.8),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: const AdaptiveProgressRing(),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }
}
