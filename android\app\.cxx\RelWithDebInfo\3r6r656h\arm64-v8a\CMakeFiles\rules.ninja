# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: Project
# Configurations: RelWithDebInfo
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/home/<USER>/Apps/flutter/packages/flutter_tools/gradle/src/main/groovy -B/home/<USER>/Development/Jhelum/gyawun-app/android/app/.cxx/RelWithDebInfo/3r6r656h/arm64-v8a
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/ninja -t targets
  description = All primary targets available:

