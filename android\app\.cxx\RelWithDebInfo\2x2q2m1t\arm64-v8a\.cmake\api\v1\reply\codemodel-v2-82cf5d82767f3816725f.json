{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/home/<USER>/Development/Jhelum/gyawun-app/android/app/.cxx/RelWithDebInfo/2x2q2m1t/arm64-v8a", "source": "/home/<USER>/Apps/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}