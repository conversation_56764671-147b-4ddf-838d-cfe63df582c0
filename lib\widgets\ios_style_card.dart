import 'dart:io';
import 'package:flutter/material.dart';

class IOSStyleCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onSecondaryTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double borderRadius;
  final bool enableAnimation;
  final double? width;
  final double? height;

  const IOSStyleCard({
    super.key,
    required this.child,
    this.onTap,
    this.onSecondaryTap,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius = 16.0,
    this.enableAnimation = true,
    this.width,
    this.height,
  });

  @override
  State<IOSStyleCard> createState() => _IOSStyleCardState();
}

class _IOSStyleCardState extends State<IOSStyleCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _shadowAnimation = Tween<double>(
      begin: 1.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.enableAnimation) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.enableAnimation) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.enableAnimation) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final defaultBackgroundColor = isDark 
        ? Colors.grey[900]?.withOpacity(0.8) 
        : Colors.white.withOpacity(0.9);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.width,
            height: widget.height,
            margin: widget.margin ?? const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? defaultBackgroundColor,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              boxShadow: [
                BoxShadow(
                  color: isDark 
                      ? Colors.black.withOpacity(0.3 * _shadowAnimation.value)
                      : Colors.black.withOpacity(0.1 * _shadowAnimation.value),
                  blurRadius: 12 * _shadowAnimation.value,
                  offset: Offset(0, 4 * _shadowAnimation.value),
                  spreadRadius: 0,
                ),
                if (!isDark)
                  BoxShadow(
                    color: Colors.white.withOpacity(0.8 * _shadowAnimation.value),
                    blurRadius: 8 * _shadowAnimation.value,
                    offset: Offset(0, -2 * _shadowAnimation.value),
                    spreadRadius: 0,
                  ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              child: InkWell(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                onTap: widget.onTap,
                onSecondaryTap: widget.onSecondaryTap,
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                child: Padding(
                  padding: widget.padding ?? const EdgeInsets.all(12),
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class IOSStyleChip extends StatefulWidget {
  final String text;
  final VoidCallback? onTap;
  final bool isSelected;
  final Color? selectedColor;

  const IOSStyleChip({
    super.key,
    required this.text,
    this.onTap,
    this.isSelected = false,
    this.selectedColor,
  });

  @override
  State<IOSStyleChip> createState() => _IOSStyleChipState();
}

class _IOSStyleChipState extends State<IOSStyleChip>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: widget.onTap,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                color: widget.isSelected
                    ? (widget.selectedColor ?? theme.colorScheme.primary)
                    : (isDark 
                        ? Colors.grey[800]?.withOpacity(0.6)
                        : Colors.grey[200]?.withOpacity(0.8)),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  if (widget.isSelected)
                    BoxShadow(
                      color: (widget.selectedColor ?? theme.colorScheme.primary)
                          .withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                ],
              ),
              child: Text(
                widget.text,
                style: TextStyle(
                  color: widget.isSelected
                      ? Colors.white
                      : theme.textTheme.bodyMedium?.color,
                  fontWeight: widget.isSelected 
                      ? FontWeight.w600 
                      : FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
