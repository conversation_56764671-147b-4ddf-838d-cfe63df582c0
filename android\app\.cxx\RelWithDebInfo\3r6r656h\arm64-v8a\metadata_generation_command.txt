                        -H/home/<USER>/Apps/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/home/<USER>/Apps/Android/Sdk/ndk/27.0.12077973
-DCMAKE_ANDROID_NDK=/home/<USER>/Apps/Android/Sdk/ndk/27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Apps/Android/Sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Apps/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/Development/Jhelum/gyawun-app/build/app/intermediates/cxx/RelWithDebInfo/3r6r656h/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/Development/Jhelum/gyawun-app/build/app/intermediates/cxx/RelWithDebInfo/3r6r656h/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/home/<USER>/Development/Jhelum/gyawun-app/android/app/.cxx/RelWithDebInfo/3r6r656h/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2