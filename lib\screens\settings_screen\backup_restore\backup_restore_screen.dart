import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../generated/l10n.dart';
import '../../../themes/text_styles.dart';
import '../../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../../widgets/ios_style_settings_tile.dart';
import 'backup_restore_screen_data.dart';

class BackupRestoreScreen extends StatelessWidget {
  const BackupRestoreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AdaptiveScaffold(
      appBar: AdaptiveAppBar(
        title: Text(S.of(context).Backup_And_Restore,
            style: mediumTextStyle(context, bold: false)),
        centerTitle: true,
      ),
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 1000),
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
            children: [
              IOSStyleSettingsGroup(
                title: "Backup Your Data",
                children: backupRestoreScreenData(context).map((e) {
                  return IOSStyleSettingsTile(
                    title: e.title,
                    leading: (e.icon != null)
                        ? IOSStyleColorIcon(
                            color: e.color ?? Theme.of(context).colorScheme.primary,
                            icon: e.icon!,
                          )
                        : null,
                    trailing: e.trailing != null ? e.trailing!(context) : null,
                    showChevron: e.hasNavigation,
                    onTap: (e.hasNavigation && e.location != null) || e.onTap != null
                        ? () {
                            if (e.hasNavigation && e.location != null) {
                              context.go(e.location!);
                            } else if (e.onTap != null) {
                              e.onTap!(context);
                            }
                          }
                        : null,
                    subtitle: e.subtitle != null ? e.subtitle!(context) : null,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
