id: com.jhelum.gyawun
runtime: org.gnome.Platform
runtime-version: '48'
sdk: org.gnome.Sdk

command: gyawun
finish-args:
  # X11/Wayland access
  - --share=ipc
  - --socket=fallback-x11
  - --socket=wayland
  # Audio playback
  - --socket=pulseaudio
  # Network access for Jellyfin streaming
  - --share=network
  # File access for downloads/media
  - --filesystem=home
  # Allow access to PipeWire socket for mpv
  - --filesystem=xdg-run/pipewire-0:ro
  # Hardware acceleration
  - --device=dri
  # Allow notification access
  - --talk-name=org.freedesktop.Notifications
  - --talk-name=org.freedesktop.PowerManagement
  - --talk-name=org.freedesktop.ScreenSaver
  - --talk-name=org.freedesktop.portal.*

modules:
  - name: gyawun
    buildsystem: simple
    build-commands:
      - mkdir -p /app/bin
      - cp -r bundle/* /app/bin/
      - chmod +x /app/bin/gyawun
      - mkdir -p /app/share/applications
      - mkdir -p /app/share/icons/hicolor/scalable/apps
      - install -Dm644 gyawun.desktop /app/share/applications/${FLATPAK_ID}.desktop
      - install -Dm644 com.jhelum.gyawun.png /app/share/icons/hicolor/scalable/apps/${FLATPAK_ID}.png
    sources:
      - type: dir
        path: .