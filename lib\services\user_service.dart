import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

class UserService extends ChangeNotifier {
  static const String _usernameKey = 'USERNAME';
  static const String _onboardingCompleteKey = 'ONBOARDING_COMPLETE';
  static const String _firstLaunchKey = 'FIRST_LAUNCH';
  
  late Box _box;
  String? _username;
  bool _isOnboardingComplete = false;
  bool _isFirstLaunch = true;

  UserService() {
    _box = Hive.box('SETTINGS');
    _loadUserData();
  }

  // Getters
  String? get username => _username;
  bool get isOnboardingComplete => _isOnboardingComplete;
  bool get isFirstLaunch => _isFirstLaunch;
  bool get hasUsername => _username != null && _username!.isNotEmpty;

  // Load user data from storage
  void _loadUserData() {
    _username = _box.get(_usernameKey);
    _isOnboardingComplete = _box.get(_onboardingCompleteKey, defaultValue: false);
    _isFirstLaunch = _box.get(_firstLaunchKey, defaultValue: true);
    
    // If user has completed onboarding but we don't have username, reset onboarding
    if (_isOnboardingComplete && !hasUsername) {
      _isOnboardingComplete = false;
      _box.put(_onboardingCompleteKey, false);
    }
    
    notifyListeners();
  }

  // Set username
  Future<void> setUsername(String username) async {
    if (username.trim().isEmpty) return;
    
    _username = username.trim();
    await _box.put(_usernameKey, _username);
    notifyListeners();
  }

  // Complete onboarding
  Future<void> completeOnboarding() async {
    _isOnboardingComplete = true;
    _isFirstLaunch = false;
    
    await _box.put(_onboardingCompleteKey, true);
    await _box.put(_firstLaunchKey, false);
    
    notifyListeners();
  }

  // Reset onboarding (for testing purposes)
  Future<void> resetOnboarding() async {
    _username = null;
    _isOnboardingComplete = false;
    _isFirstLaunch = true;
    
    await _box.delete(_usernameKey);
    await _box.put(_onboardingCompleteKey, false);
    await _box.put(_firstLaunchKey, true);
    
    notifyListeners();
  }

  // Update username
  Future<void> updateUsername(String newUsername) async {
    if (newUsername.trim().isEmpty) return;
    
    _username = newUsername.trim();
    await _box.put(_usernameKey, _username);
    notifyListeners();
  }

  // Get greeting based on time of day
  String getGreeting() {
    final hour = DateTime.now().hour;
    final name = _username ?? 'User';
    
    if (hour < 12) {
      return 'Good Morning, $name';
    } else if (hour < 17) {
      return 'Good Afternoon, $name';
    } else {
      return 'Good Evening, $name';
    }
  }

  // Get display name
  String getDisplayName() {
    return _username ?? 'Music Lover';
  }

  // Check if should show onboarding
  bool shouldShowOnboarding() {
    return !_isOnboardingComplete || !hasUsername;
  }

  // Mark first launch as complete
  Future<void> markFirstLaunchComplete() async {
    _isFirstLaunch = false;
    await _box.put(_firstLaunchKey, false);
    notifyListeners();
  }

  // Get user stats (can be expanded later)
  Map<String, dynamic> getUserStats() {
    return {
      'username': _username,
      'hasCompletedOnboarding': _isOnboardingComplete,
      'isFirstLaunch': _isFirstLaunch,
      'joinDate': _box.get('JOIN_DATE', defaultValue: DateTime.now().toIso8601String()),
    };
  }

  // Set join date
  Future<void> setJoinDate() async {
    if (_box.get('JOIN_DATE') == null) {
      await _box.put('JOIN_DATE', DateTime.now().toIso8601String());
    }
  }

  // Validate username
  static bool isValidUsername(String username) {
    final trimmed = username.trim();
    return trimmed.isNotEmpty && 
           trimmed.length >= 2 && 
           trimmed.length <= 30 &&
           RegExp(r'^[a-zA-Z0-9\s._-]+$').hasMatch(trimmed);
  }

  // Get username validation error
  static String? getUsernameError(String username) {
    final trimmed = username.trim();
    
    if (trimmed.isEmpty) {
      return 'Please enter your name';
    }
    
    if (trimmed.length < 2) {
      return 'Name must be at least 2 characters';
    }
    
    if (trimmed.length > 30) {
      return 'Name must be less than 30 characters';
    }
    
    if (!RegExp(r'^[a-zA-Z0-9\s._-]+$').hasMatch(trimmed)) {
      return 'Name can only contain letters, numbers, spaces, dots, underscores, and hyphens';
    }
    
    return null;
  }
}
