// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a es locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'es';

  static String m0(count) =>
      "${Intl.plural(count, zero: 'No hay canciones', one: '1 canción', other: '${count} canciones')}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "About": MessageLookupByLibrary.simpleMessage("Acerca de"),
    "Add_To_Favourites": MessageLookupByLibrary.simpleMessage(
      "Añadir a favoritos",
    ),
    "Add_To_Library": MessageLookupByLibrary.simpleMessage(
      "Añadir a la biblioteca",
    ),
    "Add_To_Playlist": MessageLookupByLibrary.simpleMessage(
      "Añadir a la lista de reproducción",
    ),
    "Add_To_Queue": MessageLookupByLibrary.simpleMessage("Añadir a la cola"),
    "Album": MessageLookupByLibrary.simpleMessage("Álbum"),
    "Albums": MessageLookupByLibrary.simpleMessage("Álbumes"),
    "Appearence": MessageLookupByLibrary.simpleMessage("Apariencia"),
    "Artists": MessageLookupByLibrary.simpleMessage("Artistas"),
    "Audio_And_Playback": MessageLookupByLibrary.simpleMessage(
      "Audio y reproducción",
    ),
    "Backup": MessageLookupByLibrary.simpleMessage("Copia de seguridad"),
    "Backup_And_Restore": MessageLookupByLibrary.simpleMessage(
      "Copia de seguridad y restauración",
    ),
    "Battery_Optimisation_message": MessageLookupByLibrary.simpleMessage(
      "Haz clic aquí para desactivar la optimización de batería para que Gyawun funcione correctamente",
    ),
    "Battery_Optimisation_title": MessageLookupByLibrary.simpleMessage(
      "Optimización de batería detectada",
    ),
    "Bug_Report": MessageLookupByLibrary.simpleMessage("Reporte de errores"),
    "Buy_Me_A_Coffee": MessageLookupByLibrary.simpleMessage("Cómprame un café"),
    "Cancel": MessageLookupByLibrary.simpleMessage("Cancelar"),
    "Check_For_Update": MessageLookupByLibrary.simpleMessage(
      "Buscar actualizaciones",
    ),
    "Confirm": MessageLookupByLibrary.simpleMessage("Confirmar"),
    "Content": MessageLookupByLibrary.simpleMessage("Contenido"),
    "Contributors": MessageLookupByLibrary.simpleMessage("Colaboradores"),
    "Copied_To_Clipboard": MessageLookupByLibrary.simpleMessage(
      "Copiado al portapapeles",
    ),
    "Country": MessageLookupByLibrary.simpleMessage("País"),
    "Create": MessageLookupByLibrary.simpleMessage("Crear"),
    "Create_Playlist": MessageLookupByLibrary.simpleMessage(
      "Crear lista de reproducción",
    ),
    "DOwnload_Quality": MessageLookupByLibrary.simpleMessage(
      "Calidad de descarga",
    ),
    "Delete_Item_Message": MessageLookupByLibrary.simpleMessage(
      "¿Estás seguro de que quieres eliminar este elemento?",
    ),
    "Delete_Playback_History": MessageLookupByLibrary.simpleMessage(
      "Eliminar historial de reproducción",
    ),
    "Delete_Playback_History_Confirm_Message":
        MessageLookupByLibrary.simpleMessage(
          "¿Estás seguro de que quieres eliminar el historial de reproducción?",
        ),
    "Delete_Search_History": MessageLookupByLibrary.simpleMessage(
      "Eliminar historial de búsqueda",
    ),
    "Delete_Search_History_Confirm_Message":
        MessageLookupByLibrary.simpleMessage(
          "¿Estás seguro de que quieres eliminar el historial de búsqueda?",
        ),
    "Developer": MessageLookupByLibrary.simpleMessage("Desarrollador"),
    "Donate": MessageLookupByLibrary.simpleMessage("Donar"),
    "Donate_Message": MessageLookupByLibrary.simpleMessage(
      "Apoya el desarrollo de Gyawun",
    ),
    "Done": MessageLookupByLibrary.simpleMessage("Hecho"),
    "Download": MessageLookupByLibrary.simpleMessage("Descargar"),
    "Downloads": MessageLookupByLibrary.simpleMessage("Descargas"),
    "Dynamic_Colors": MessageLookupByLibrary.simpleMessage("Colores dinámicos"),
    "Enable_Equalizer": MessageLookupByLibrary.simpleMessage(
      "Activar ecualizador",
    ),
    "Enable_Playback_History": MessageLookupByLibrary.simpleMessage(
      "Activar historial de reproducción",
    ),
    "Enable_Search_History": MessageLookupByLibrary.simpleMessage(
      "Activar historial de búsqueda",
    ),
    "Enter_Visitor_Id": MessageLookupByLibrary.simpleMessage(
      "Introducir ID de visitante",
    ),
    "Equalizer": MessageLookupByLibrary.simpleMessage("Ecualizador"),
    "Favourites": MessageLookupByLibrary.simpleMessage("Favoritos"),
    "Feature_Request": MessageLookupByLibrary.simpleMessage(
      "Solicitud de características",
    ),
    "Google_Account": MessageLookupByLibrary.simpleMessage("Cuenta de Google"),
    "Gyawun": MessageLookupByLibrary.simpleMessage("Gyawun"),
    "High": MessageLookupByLibrary.simpleMessage("Alto"),
    "History": MessageLookupByLibrary.simpleMessage("Historial"),
    "Home": MessageLookupByLibrary.simpleMessage("Inicio"),
    "Import": MessageLookupByLibrary.simpleMessage("Importar"),
    "Import_Playlist": MessageLookupByLibrary.simpleMessage(
      "Importar lista de reproducción",
    ),
    "Jhelum_Corp": MessageLookupByLibrary.simpleMessage("Jhelum Corp"),
    "Language": MessageLookupByLibrary.simpleMessage("Idioma"),
    "Loudness_And_Equalizer": MessageLookupByLibrary.simpleMessage(
      "Volumen y ecualizador",
    ),
    "Loudness_Enhancer": MessageLookupByLibrary.simpleMessage(
      "Mejorador de volumen",
    ),
    "Low": MessageLookupByLibrary.simpleMessage("Bajo"),
    "Made_In_Kashmir": MessageLookupByLibrary.simpleMessage(
      "Hecho en Cachemira",
    ),
    "Name": MessageLookupByLibrary.simpleMessage("Nombre"),
    "Next_Up": MessageLookupByLibrary.simpleMessage("Siguiente"),
    "No": MessageLookupByLibrary.simpleMessage("No"),
    "Organisation": MessageLookupByLibrary.simpleMessage("Organización"),
    "Pay_With_UPI": MessageLookupByLibrary.simpleMessage("Pagar con UPI"),
    "Payment_Methods": MessageLookupByLibrary.simpleMessage("Métodos de pago"),
    "Personalised_Content": MessageLookupByLibrary.simpleMessage(
      "Contenido personalizado",
    ),
    "Play_Next": MessageLookupByLibrary.simpleMessage("Reproducir siguiente"),
    "Playback_History_Deleted": MessageLookupByLibrary.simpleMessage(
      "Historial de reproducción eliminado",
    ),
    "Playlist_Name": MessageLookupByLibrary.simpleMessage("Nombre de la lista"),
    "Playlists": MessageLookupByLibrary.simpleMessage("Listas de reproducción"),
    "Progress": MessageLookupByLibrary.simpleMessage("Progreso"),
    "Remove": MessageLookupByLibrary.simpleMessage("Eliminar"),
    "Remove_All_History_Message": MessageLookupByLibrary.simpleMessage(
      "¿Estás seguro de que quieres borrar todo el historial?",
    ),
    "Remove_From_Favourites": MessageLookupByLibrary.simpleMessage(
      "Eliminar de favoritos",
    ),
    "Remove_From_Library": MessageLookupByLibrary.simpleMessage(
      "Eliminar de la biblioteca",
    ),
    "Remove_From_YTMusic_Message": MessageLookupByLibrary.simpleMessage(
      "¿Estás seguro de que quieres eliminarlo de YTMusic?",
    ),
    "Remove_Message": MessageLookupByLibrary.simpleMessage(
      "¿Estás seguro de que quieres eliminarlo?",
    ),
    "Rename": MessageLookupByLibrary.simpleMessage("Renombrar"),
    "Rename_Playlist": MessageLookupByLibrary.simpleMessage(
      "Renombrar lista de reproducción",
    ),
    "Reset_Visitor_Id": MessageLookupByLibrary.simpleMessage(
      "Restablecer ID de visitante",
    ),
    "Restore": MessageLookupByLibrary.simpleMessage("Restaurar"),
    "Saved": MessageLookupByLibrary.simpleMessage("Guardado"),
    "Search_Gyawun": MessageLookupByLibrary.simpleMessage("Buscar Gyawun"),
    "Search_History_Deleted": MessageLookupByLibrary.simpleMessage(
      "Historial de búsqueda eliminado",
    ),
    "Search_Settings": MessageLookupByLibrary.simpleMessage(
      "Configuración de búsqueda",
    ),
    "Select_Backup": MessageLookupByLibrary.simpleMessage(
      "Seleccionar copia de seguridad",
    ),
    "Settings": MessageLookupByLibrary.simpleMessage("Configuraciones"),
    "Sheikh_Haziq": MessageLookupByLibrary.simpleMessage("Sheikh Haziq"),
    "Show_Less": MessageLookupByLibrary.simpleMessage("Mostrar menos"),
    "Show_More": MessageLookupByLibrary.simpleMessage("Mostrar más"),
    "Shuffle": MessageLookupByLibrary.simpleMessage("Aleatorio"),
    "Skip_Silence": MessageLookupByLibrary.simpleMessage("Saltar silencio"),
    "Sleep_Timer": MessageLookupByLibrary.simpleMessage(
      "Temporizador de apagado",
    ),
    "Songs": MessageLookupByLibrary.simpleMessage("Canciones"),
    "Songs_Will_Start_Playing_Soon": MessageLookupByLibrary.simpleMessage(
      "Las canciones comenzarán a reproducirse pronto.",
    ),
    "Source_Code": MessageLookupByLibrary.simpleMessage("Código fuente"),
    "Start_Radio": MessageLookupByLibrary.simpleMessage("Iniciar radio"),
    "Streaming_Quality": MessageLookupByLibrary.simpleMessage(
      "Calidad de transmisión",
    ),
    "Subscriptions": MessageLookupByLibrary.simpleMessage("Suscripciones"),
    "Support_Me_On_Kofi": MessageLookupByLibrary.simpleMessage(
      "Apóyame en Ko-fi",
    ),
    "Telegram": MessageLookupByLibrary.simpleMessage("Telegram"),
    "Theme_Mode": MessageLookupByLibrary.simpleMessage("Modo de tema"),
    "Version": MessageLookupByLibrary.simpleMessage("Versión"),
    "Visitor_Id": MessageLookupByLibrary.simpleMessage("ID de visitante"),
    "Window_Effect": MessageLookupByLibrary.simpleMessage("Efecto de ventana"),
    "YTMusic": MessageLookupByLibrary.simpleMessage("YTMusic"),
    "Yes": MessageLookupByLibrary.simpleMessage("Sí"),
    "nSongs": m0,
  };
}
