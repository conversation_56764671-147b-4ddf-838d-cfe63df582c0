// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a hi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'hi';

  static String m0(count) =>
      "${Intl.plural(count, zero: 'कोई गाने नहीं', one: '1 गाना', other: '${count} गाने')}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "About": MessageLookupByLibrary.simpleMessage("के बारे में"),
    "Add_To_Favourites": MessageLookupByLibrary.simpleMessage(
      "पसंदीदा में जोड़ें",
    ),
    "Add_To_Library": MessageLookupByLibrary.simpleMessage(
      "लाइब्रेरी में जोड़ें",
    ),
    "Add_To_Playlist": MessageLookupByLibrary.simpleMessage(
      "प्लेलिस्ट में जोड़ें",
    ),
    "Add_To_Queue": MessageLookupByLibrary.simpleMessage("कतार में जोड़ें"),
    "Album": MessageLookupByLibrary.simpleMessage("एल्बम"),
    "Albums": MessageLookupByLibrary.simpleMessage("एल्बम"),
    "Appearence": MessageLookupByLibrary.simpleMessage("दिखावट"),
    "Artists": MessageLookupByLibrary.simpleMessage("कलाकार"),
    "Audio_And_Playback": MessageLookupByLibrary.simpleMessage(
      "ऑडियो और प्लेबैक",
    ),
    "Backup": MessageLookupByLibrary.simpleMessage("बैकअप"),
    "Backup_And_Restore": MessageLookupByLibrary.simpleMessage(
      "बैकअप और पुनर्स्थापना",
    ),
    "Battery_Optimisation_message": MessageLookupByLibrary.simpleMessage(
      "ग्यावुन को ठीक से काम करने के लिए बैटरी ऑप्टिमाइजेशन को अक्षम करने के लिए यहां क्लिक करें",
    ),
    "Battery_Optimisation_title": MessageLookupByLibrary.simpleMessage(
      "बैटरी ऑप्टिमाइजेशन पता चला",
    ),
    "Bug_Report": MessageLookupByLibrary.simpleMessage("बग रिपोर्ट"),
    "Buy_Me_A_Coffee": MessageLookupByLibrary.simpleMessage(
      "मुझे एक कॉफी खरीदें",
    ),
    "Cancel": MessageLookupByLibrary.simpleMessage("रद्द करें"),
    "Check_For_Update": MessageLookupByLibrary.simpleMessage(
      "अपडेट के लिए जाँचें",
    ),
    "Confirm": MessageLookupByLibrary.simpleMessage("पुष्टि करें"),
    "Content": MessageLookupByLibrary.simpleMessage("सामग्री"),
    "Contributors": MessageLookupByLibrary.simpleMessage("योगदानकर्ता"),
    "Copied_To_Clipboard": MessageLookupByLibrary.simpleMessage(
      "क्लिपबोर्ड पर कॉपी किया गया",
    ),
    "Country": MessageLookupByLibrary.simpleMessage("देश"),
    "Create": MessageLookupByLibrary.simpleMessage("बनाएं"),
    "Create_Playlist": MessageLookupByLibrary.simpleMessage("प्लेलिस्ट बनाएं"),
    "DOwnload_Quality": MessageLookupByLibrary.simpleMessage(
      "डाउनलोड गुणवत्ता",
    ),
    "Delete_Item_Message": MessageLookupByLibrary.simpleMessage(
      "क्या आप वाकई इस आइटम को हटाना चाहते हैं?",
    ),
    "Delete_Playback_History": MessageLookupByLibrary.simpleMessage(
      "प्लेबैक इतिहास हटाएं",
    ),
    "Delete_Playback_History_Confirm_Message":
        MessageLookupByLibrary.simpleMessage(
          "क्या आप वाकई प्लेबैक इतिहास हटाना चाहते हैं।",
        ),
    "Delete_Search_History": MessageLookupByLibrary.simpleMessage(
      "खोज इतिहास हटाएं",
    ),
    "Delete_Search_History_Confirm_Message":
        MessageLookupByLibrary.simpleMessage(
          "क्या आप वाकई खोज इतिहास हटाना चाहते हैं।",
        ),
    "Developer": MessageLookupByLibrary.simpleMessage("डेवलपर"),
    "Donate": MessageLookupByLibrary.simpleMessage("दान करें"),
    "Donate_Message": MessageLookupByLibrary.simpleMessage(
      "ग्यावुन के विकास का समर्थन करें",
    ),
    "Done": MessageLookupByLibrary.simpleMessage("हो गया"),
    "Download": MessageLookupByLibrary.simpleMessage("डाउनलोड"),
    "Downloads": MessageLookupByLibrary.simpleMessage("डाउनलोड"),
    "Dynamic_Colors": MessageLookupByLibrary.simpleMessage("डायनामिक रंग"),
    "Enable_Equalizer": MessageLookupByLibrary.simpleMessage(
      "इक्वलाइज़र सक्षम करें",
    ),
    "Enable_Playback_History": MessageLookupByLibrary.simpleMessage(
      "प्लेबैक इतिहास सक्षम करें",
    ),
    "Enable_Search_History": MessageLookupByLibrary.simpleMessage(
      "खोज इतिहास सक्षम करें",
    ),
    "Enter_Visitor_Id": MessageLookupByLibrary.simpleMessage(
      "विज़िटर आईडी दर्ज करें",
    ),
    "Equalizer": MessageLookupByLibrary.simpleMessage("इक्वलाइज़र"),
    "Favourites": MessageLookupByLibrary.simpleMessage("पसंदीदा"),
    "Feature_Request": MessageLookupByLibrary.simpleMessage("फीचर अनुरोध"),
    "Google_Account": MessageLookupByLibrary.simpleMessage("गूगल अकाउंट"),
    "Gyawun": MessageLookupByLibrary.simpleMessage("ग्यावुन"),
    "High": MessageLookupByLibrary.simpleMessage("उच्च"),
    "History": MessageLookupByLibrary.simpleMessage("इतिहास"),
    "Home": MessageLookupByLibrary.simpleMessage("होम"),
    "Import": MessageLookupByLibrary.simpleMessage("आयात करें"),
    "Import_Playlist": MessageLookupByLibrary.simpleMessage(
      "प्लेलिस्ट आयात करें",
    ),
    "Jhelum_Corp": MessageLookupByLibrary.simpleMessage("झेलम कॉर्प"),
    "Language": MessageLookupByLibrary.simpleMessage("भाषा"),
    "Loudness_And_Equalizer": MessageLookupByLibrary.simpleMessage(
      "लाउडनेस और इक्वलाइज़र",
    ),
    "Loudness_Enhancer": MessageLookupByLibrary.simpleMessage(
      "लाउडनेस एन्हांसर",
    ),
    "Low": MessageLookupByLibrary.simpleMessage("निम्न"),
    "Made_In_Kashmir": MessageLookupByLibrary.simpleMessage("कश्मीर में बना"),
    "Name": MessageLookupByLibrary.simpleMessage("नाम"),
    "Next_Up": MessageLookupByLibrary.simpleMessage("अगला"),
    "No": MessageLookupByLibrary.simpleMessage("नहीं"),
    "Organisation": MessageLookupByLibrary.simpleMessage("संगठन"),
    "Pay_With_UPI": MessageLookupByLibrary.simpleMessage(
      "UPI के साथ भुगतान करें",
    ),
    "Payment_Methods": MessageLookupByLibrary.simpleMessage("भुगतान के तरीके"),
    "Personalised_Content": MessageLookupByLibrary.simpleMessage(
      "व्यक्तिगत सामग्री",
    ),
    "Play_Next": MessageLookupByLibrary.simpleMessage("अगला चलाएं"),
    "Playback_History_Deleted": MessageLookupByLibrary.simpleMessage(
      "प्लेबैक इतिहास हटाया गया",
    ),
    "Playlist_Name": MessageLookupByLibrary.simpleMessage("प्लेलिस्ट का नाम"),
    "Playlists": MessageLookupByLibrary.simpleMessage("प्लेलिस्ट"),
    "Progress": MessageLookupByLibrary.simpleMessage("प्रगति"),
    "Remove": MessageLookupByLibrary.simpleMessage("हटाएं"),
    "Remove_All_History_Message": MessageLookupByLibrary.simpleMessage(
      "क्या आप वाकई सभी इतिहास साफ़ करना चाहते हैं?",
    ),
    "Remove_From_Favourites": MessageLookupByLibrary.simpleMessage(
      "पसंदीदा से हटाएं",
    ),
    "Remove_From_Library": MessageLookupByLibrary.simpleMessage(
      "लाइब्रेरी से हटाएं",
    ),
    "Remove_From_YTMusic_Message": MessageLookupByLibrary.simpleMessage(
      "क्या आप वाकई इसे YT संगीत से हटाना चाहते हैं?",
    ),
    "Remove_Message": MessageLookupByLibrary.simpleMessage(
      "क्या आप वाकई इसे हटाना चाहते हैं?",
    ),
    "Rename": MessageLookupByLibrary.simpleMessage("नाम बदलें"),
    "Rename_Playlist": MessageLookupByLibrary.simpleMessage(
      "प्लेलिस्ट का नाम बदलें",
    ),
    "Reset_Visitor_Id": MessageLookupByLibrary.simpleMessage(
      "विज़िटर आईडी रीसेट करें",
    ),
    "Restore": MessageLookupByLibrary.simpleMessage("पुनर्स्थापित"),
    "Saved": MessageLookupByLibrary.simpleMessage("सहेजे गए"),
    "Search_Gyawun": MessageLookupByLibrary.simpleMessage("ग्यावुन खोजें"),
    "Search_History_Deleted": MessageLookupByLibrary.simpleMessage(
      "खोज इतिहास हटाया गया",
    ),
    "Search_Settings": MessageLookupByLibrary.simpleMessage("खोज सेटिंग्स"),
    "Select_Backup": MessageLookupByLibrary.simpleMessage("बैकअप चुनें"),
    "Settings": MessageLookupByLibrary.simpleMessage("सेटिंग्स"),
    "Sheikh_Haziq": MessageLookupByLibrary.simpleMessage("शेख हाजिक"),
    "Show_Less": MessageLookupByLibrary.simpleMessage("कम दिखाएं"),
    "Show_More": MessageLookupByLibrary.simpleMessage("और दिखाएं"),
    "Shuffle": MessageLookupByLibrary.simpleMessage("शफल"),
    "Skip_Silence": MessageLookupByLibrary.simpleMessage("चुप्पी छोड़ें"),
    "Sleep_Timer": MessageLookupByLibrary.simpleMessage("स्लीप टाइमर"),
    "Songs": MessageLookupByLibrary.simpleMessage("गाने"),
    "Songs_Will_Start_Playing_Soon": MessageLookupByLibrary.simpleMessage(
      "गाने जल्द ही बजना शुरू हो जाएंगे।",
    ),
    "Source_Code": MessageLookupByLibrary.simpleMessage("सोर्स कोड"),
    "Start_Radio": MessageLookupByLibrary.simpleMessage("रेडियो शुरू करें"),
    "Streaming_Quality": MessageLookupByLibrary.simpleMessage(
      "स्ट्रीमिंग गुणवत्ता",
    ),
    "Subscriptions": MessageLookupByLibrary.simpleMessage("सब्सक्रिप्शन"),
    "Support_Me_On_Kofi": MessageLookupByLibrary.simpleMessage(
      "को-फ़ी पर मुझे समर्थन दें",
    ),
    "Telegram": MessageLookupByLibrary.simpleMessage("टेलीग्राम"),
    "Theme_Mode": MessageLookupByLibrary.simpleMessage("थीम मोड"),
    "Version": MessageLookupByLibrary.simpleMessage("संस्करण"),
    "Visitor_Id": MessageLookupByLibrary.simpleMessage("विज़िटर आईडी"),
    "Window_Effect": MessageLookupByLibrary.simpleMessage("विंडो प्रभाव"),
    "YTMusic": MessageLookupByLibrary.simpleMessage("YT संगीत"),
    "Yes": MessageLookupByLibrary.simpleMessage("हाँ"),
    "nSongs": m0,
  };
}
