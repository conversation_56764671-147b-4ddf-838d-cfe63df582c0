import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class IOSStyleColors {
  // iOS System Colors
  static const Color systemBlue = Color(0xFF007AFF);
  static const Color systemGreen = Color(0xFF34C759);
  static const Color systemIndigo = Color(0xFF5856D6);
  static const Color systemOrange = Color(0xFFFF9500);
  static const Color systemPink = Color(0xFFFF2D92);
  static const Color systemPurple = Color(0xFFAF52DE);
  static const Color systemRed = Color(0xFFFF3B30);
  static const Color systemTeal = Color(0xFF5AC8FA);
  static const Color systemYellow = Color(0xFFFFCC00);

  // Spotify-inspired colors
  static const Color spotifyGreen = Color(0xFF1DB954);
  static const Color spotifyBlack = Color(0xFF191414);
  static const Color spotifyDarkGrey = Color(0xFF121212);
  static const Color spotifyGrey = Color(0xFF535353);
  static const Color spotifyLightGrey = Color(0xFFB3B3B3);
  static const Color spotifyWhite = Color(0xFFFFFFFF);

  // iOS Background Colors
  static const Color lightBackground = Color(0xFFF2F2F7);
  static const Color lightSecondaryBackground = Color(0xFFFFFFFF);
  static const Color lightTertiaryBackground = Color(0xFFFFFFFF);

  static const Color darkBackground = Color(0xFF000000);
  static const Color darkSecondaryBackground = Color(0xFF1C1C1E);
  static const Color darkTertiaryBackground = Color(0xFF2C2C2E);

  // iOS Label Colors
  static const Color lightPrimaryLabel = Color(0xFF000000);
  static const Color lightSecondaryLabel = Color(0xFF3C3C43);
  static const Color lightTertiaryLabel = Color(0xFF3C3C43);

  static const Color darkPrimaryLabel = Color(0xFFFFFFFF);
  static const Color darkSecondaryLabel = Color(0xFFEBEBF5);
  static const Color darkTertiaryLabel = Color(0xFFEBEBF5);

  // iOS Fill Colors
  static const Color lightQuaternaryFill = Color(0x0F767680);
  static const Color lightTertiaryFill = Color(0x1E767680);
  static const Color lightSecondaryFill = Color(0x28767680);
  static const Color lightPrimaryFill = Color(0x33767680);

  static const Color darkQuaternaryFill = Color(0x14787880);
  static const Color darkTertiaryFill = Color(0x1E787880);
  static const Color darkSecondaryFill = Color(0x28787880);
  static const Color darkPrimaryFill = Color(0x33787880);
}

ThemeData iosStyleLightTheme({Color? accentColor}) {
  final accent = accentColor ?? IOSStyleColors.spotifyGreen;
  
  return ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    fontFamily: GoogleFonts.inter().fontFamily,
    
    colorScheme: ColorScheme.light(
      primary: accent,
      onPrimary: Colors.white,
      secondary: IOSStyleColors.systemBlue,
      onSecondary: Colors.white,
      surface: IOSStyleColors.lightSecondaryBackground,
      onSurface: IOSStyleColors.lightPrimaryLabel,
      background: IOSStyleColors.lightBackground,
      onBackground: IOSStyleColors.lightPrimaryLabel,
      error: IOSStyleColors.systemRed,
      onError: Colors.white,
      outline: IOSStyleColors.lightSecondaryLabel.withOpacity(0.3),
      surfaceVariant: IOSStyleColors.lightTertiaryBackground,
      onSurfaceVariant: IOSStyleColors.lightSecondaryLabel,
    ),

    scaffoldBackgroundColor: Platform.isWindows 
        ? Colors.transparent 
        : IOSStyleColors.lightBackground,

    appBarTheme: AppBarTheme(
      backgroundColor: IOSStyleColors.lightSecondaryBackground.withOpacity(0.9),
      foregroundColor: IOSStyleColors.lightPrimaryLabel,
      elevation: 0,
      scrolledUnderElevation: 0,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: GoogleFonts.inter(
        fontSize: 17,
        fontWeight: FontWeight.w600,
        color: IOSStyleColors.lightPrimaryLabel,
      ),
    ),

    cardTheme: CardThemeData(
      color: IOSStyleColors.lightSecondaryBackground,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      shadowColor: Colors.black.withOpacity(0.1),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: accent,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),

    textTheme: GoogleFonts.interTextTheme().copyWith(
      displayLarge: GoogleFonts.inter(
        fontSize: 34,
        fontWeight: FontWeight.w700,
        color: IOSStyleColors.lightPrimaryLabel,
        letterSpacing: -0.5,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: 28,
        fontWeight: FontWeight.w700,
        color: IOSStyleColors.lightPrimaryLabel,
        letterSpacing: -0.3,
      ),
      headlineLarge: GoogleFonts.inter(
        fontSize: 24,
        fontWeight: FontWeight.w700,
        color: IOSStyleColors.lightPrimaryLabel,
        letterSpacing: -0.2,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: IOSStyleColors.lightPrimaryLabel,
      ),
      bodyLarge: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: IOSStyleColors.lightPrimaryLabel,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: IOSStyleColors.lightSecondaryLabel,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: IOSStyleColors.lightTertiaryLabel,
      ),
    ),

    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: IOSStyleColors.lightSecondaryBackground.withOpacity(0.9),
      selectedItemColor: accent,
      unselectedItemColor: IOSStyleColors.lightSecondaryLabel,
      elevation: 0,
      type: BottomNavigationBarType.fixed,
    ),

    dividerTheme: DividerThemeData(
      color: IOSStyleColors.lightSecondaryLabel.withOpacity(0.2),
      thickness: 0.5,
    ),
  );
}

ThemeData iosStyleDarkTheme({Color? accentColor}) {
  final accent = accentColor ?? IOSStyleColors.spotifyGreen;
  
  return ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    fontFamily: GoogleFonts.inter().fontFamily,
    
    colorScheme: ColorScheme.dark(
      primary: accent,
      onPrimary: Colors.black,
      secondary: IOSStyleColors.systemBlue,
      onSecondary: Colors.white,
      surface: IOSStyleColors.darkSecondaryBackground,
      onSurface: IOSStyleColors.darkPrimaryLabel,
      background: IOSStyleColors.darkBackground,
      onBackground: IOSStyleColors.darkPrimaryLabel,
      error: IOSStyleColors.systemRed,
      onError: Colors.white,
      outline: IOSStyleColors.darkSecondaryLabel.withOpacity(0.3),
      surfaceVariant: IOSStyleColors.darkTertiaryBackground,
      onSurfaceVariant: IOSStyleColors.darkSecondaryLabel,
    ),

    scaffoldBackgroundColor: Platform.isWindows 
        ? Colors.transparent 
        : IOSStyleColors.darkBackground,

    appBarTheme: AppBarTheme(
      backgroundColor: IOSStyleColors.darkSecondaryBackground.withOpacity(0.9),
      foregroundColor: IOSStyleColors.darkPrimaryLabel,
      elevation: 0,
      scrolledUnderElevation: 0,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: GoogleFonts.inter(
        fontSize: 17,
        fontWeight: FontWeight.w600,
        color: IOSStyleColors.darkPrimaryLabel,
      ),
    ),

    cardTheme: CardThemeData(
      color: IOSStyleColors.darkSecondaryBackground,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      shadowColor: Colors.black.withOpacity(0.3),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: accent,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        textStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ),

    textTheme: GoogleFonts.interTextTheme().copyWith(
      displayLarge: GoogleFonts.inter(
        fontSize: 34,
        fontWeight: FontWeight.w700,
        color: IOSStyleColors.darkPrimaryLabel,
        letterSpacing: -0.5,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: 28,
        fontWeight: FontWeight.w700,
        color: IOSStyleColors.darkPrimaryLabel,
        letterSpacing: -0.3,
      ),
      headlineLarge: GoogleFonts.inter(
        fontSize: 24,
        fontWeight: FontWeight.w700,
        color: IOSStyleColors.darkPrimaryLabel,
        letterSpacing: -0.2,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: IOSStyleColors.darkPrimaryLabel,
      ),
      bodyLarge: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: IOSStyleColors.darkPrimaryLabel,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: IOSStyleColors.darkSecondaryLabel,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: IOSStyleColors.darkTertiaryLabel,
      ),
    ),

    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: IOSStyleColors.darkSecondaryBackground.withOpacity(0.9),
      selectedItemColor: accent,
      unselectedItemColor: IOSStyleColors.darkSecondaryLabel,
      elevation: 0,
      type: BottomNavigationBarType.fixed,
    ),

    dividerTheme: DividerThemeData(
      color: IOSStyleColors.darkSecondaryLabel.withOpacity(0.2),
      thickness: 0.5,
    ),
  );
}
