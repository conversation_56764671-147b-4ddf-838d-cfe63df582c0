import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../generated/l10n.dart';
import '../../themes/colors.dart';
import '../../themes/text_styles.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../widgets/ios_style_settings_tile.dart';
import 'setting_screen_data.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  TextEditingController searchController = TextEditingController();

  String searchText = "";

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    searchController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveScaffold(
      appBar: AdaptiveAppBar(
        title: Text(S.of(context).Settings,
            style: mediumTextStyle(context, bold: false)),
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 1000),
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: AdaptiveTextField(
                  controller: searchController,
                  onChanged: (value) {
                    setState(() {
                      searchText = value;
                    });
                  },
                  autofocus: false,
                  keyboardType: TextInputType.text,
                  maxLines: 1,
                  textInputAction: TextInputAction.search,
                  fillColor:
                      Platform.isWindows ? null : darkGreyColor.withAlpha(100),
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
                  borderRadius:
                      BorderRadius.circular(Platform.isWindows ? 4.0 : 35),
                  hintText: S.of(context).Search_Settings,
                  prefix: const Icon(Icons.search),
                  suffix: searchController.text.trim().isNotEmpty
                      ? GestureDetector(
                          onTap: () {
                            searchController.text = "";
                            searchText = "";
                            setState(() {});
                          },
                          child: const Icon(CupertinoIcons.clear),
                        )
                      : null,
                ),
              ),


              IOSStyleSettingsGroup(
                children: (searchText == ""
                        ? settingScreenData(context)
                        : allSettingsData(context)
                            .where((element) => element.title
                                .toLowerCase()
                                .contains(searchText.toLowerCase()))
                            .toList())
                    .map((e) {
                  return IOSStyleSettingsTile(
                    title: e.title,
                    leading: (e.icon != null)
                        ? IOSStyleColorIcon(
                            color: e.color ?? Theme.of(context).colorScheme.primary,
                            icon: e.icon!,
                          )
                        : null,
                    trailing: e.trailing != null ? e.trailing!(context) : null,
                    showChevron: e.hasNavigation,
                    onTap: () {
                      if (e.hasNavigation && e.location != null) {
                        context.go(e.location!);
                      } else if (e.onTap != null) {
                        e.onTap!(context);
                      }
                    },
                    subtitle: e.subtitle != null ? e.subtitle!(context) : null,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}


