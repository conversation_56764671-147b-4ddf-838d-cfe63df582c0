import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:image_picker/image_picker.dart';

import '../../../generated/l10n.dart';
import '../../../themes/text_styles.dart';
import '../../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../../widgets/ios_style_card.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _bioController = TextEditingController();
  final Box _box = Hive.box('SETTINGS');
  String? _profileImagePath;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  _loadProfile() {
    _nameController.text = _box.get('PROFILE_NAME', defaultValue: '');
    _bioController.text = _box.get('PROFILE_BIO', defaultValue: '');
    _profileImagePath = _box.get('PROFILE_IMAGE_PATH');
    setState(() {});
  }

  _saveProfile() async {
    await _box.put('PROFILE_NAME', _nameController.text);
    await _box.put('PROFILE_BIO', _bioController.text);
    if (_profileImagePath != null) {
      await _box.put('PROFILE_IMAGE_PATH', _profileImagePath);
    }
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(S.of(context).Save_Profile)),
      );
    }
  }

  _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _profileImagePath = image.path;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveScaffold(
      appBar: AdaptiveAppBar(
        title: Text(S.of(context).Profile),
        centerTitle: true,
        actions: [
          AdaptiveIconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveProfile,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Photo Section
            IOSStyleCard(
              child: Column(
                children: [
                  Center(
                    child: GestureDetector(
                      onTap: _pickImage,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.grey.withValues(alpha: 0.3),
                          border: Border.all(
                            color: Theme.of(context).primaryColor,
                            width: 2,
                          ),
                        ),
                        child: _profileImagePath != null
                            ? ClipOval(
                                child: Image.file(
                                  File(_profileImagePath!),
                                  fit: BoxFit.cover,
                                  width: 120,
                                  height: 120,
                                ),
                              )
                            : Icon(
                                Icons.add_a_photo,
                                size: 40,
                                color: Theme.of(context).primaryColor,
                              ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    S.of(context).Profile_Photo,
                    style: smallTextStyle(context),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Profile Details Card
            IOSStyleCard(
              child: Column(
                children: [
                  // Name Field
                  AdaptiveTextField(
                    controller: _nameController,
                    hintText: S.of(context).Profile_Name,
                    prefix: const Icon(Icons.person),
                    fillColor: Platform.isWindows ? null : Colors.grey.withValues(alpha: 0.1),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  const SizedBox(height: 16),

                  // Bio Field
                  AdaptiveTextField(
                    controller: _bioController,
                    hintText: S.of(context).Profile_Bio,
                    prefix: const Icon(Icons.info),
                    maxLines: 3,
                    fillColor: Platform.isWindows ? null : Colors.grey.withValues(alpha: 0.1),
                    contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Save Button
            SizedBox(
              width: double.infinity,
              child: AdaptiveButton(
                onPressed: _saveProfile,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Text(
                    S.of(context).Save_Profile,
                    style: textStyle(context, bold: true),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
